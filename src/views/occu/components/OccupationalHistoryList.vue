<template>
  <a-card title="职业接触史" size="small">
    <template #extra>
      <a-button type="primary" @click="handleAdd" :disabled="disabled">
        <template #icon>
          <plus-outlined />
        </template>
        添加职业史记录
      </a-button>
    </template>

    <div class="occupational-history-list">
      <div v-if="!dataSource.length && !loading" class="empty-state">
        <a-empty description="暂无职业史记录" />
      </div>

      <a-spin :spinning="loading">
        <div class="inline-form-list">
          <div
            v-for="(record, index) in dataSource"
            :key="record.uuid || record.id || `new_${index}`"
            class="form-item-row"
            :class="{ editing: record._editMode, 'new-record': record._isNew }"
          >
            <a-form :ref="(el) => setFormRef(el, index)" :model="record" layout="horizontal" class="card-form">
              <div class="form-content">
                <a-row align="center">
                  <a-col :span="8">
                    <a-form-item label="总工龄" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="form-field work-duration">
                      <template v-if="record._editMode">
                        <div class="duration-inputs">
                          <a-input-number
                            v-model:value="record.workYears"
                            placeholder="年"
                            :min="0"
                            style="width: 60px"
                            @change="handleWorkYearsChange(record)"
                          />
                          <span>年</span>
                          <a-input-number
                            v-model:value="record.workMonths"
                            placeholder="月"
                            :min="0"
                            :max="11"
                            style="width: 60px"
                            @change="handleWorkMonthsChange(record)"
                          />
                          <span>月</span>
                          <a-button
                            type="link"
                            size="small"
                            @click="calculateDatesFromWorkYears(record)"
                            :disabled="!record.workYears && !record.workMonths"
                            title="根据工龄自动计算开始结束日期"
                          >
                            <template #icon>
                              <calculator-outlined />
                            </template>
                            计算
                          </a-button>
                        </div>
                      </template>
                      <span v-else class="field-value">
                        {{ formatDuration(record.workYears, record.workMonths) }}
                      </span>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="起止日期"
                      name="startDate"
                      :rules="[{ required: true, message: '请输入开始日期' }]"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                      class="form-field date-field"
                    >
                      <template v-if="record._editMode">
                        <div class="date-range">
                          <div class="ant-form-item-control-input">
                            <div class="ant-form-item-control-input-content">
                              <input
                                v-cleave="{ date: true, delimiter: '-', datePattern: ['Y', 'm', 'd'] }"
                                v-model="record.startDate"
                                class="ant-input css-dev-only-do-not-override-udyjmm"
                                placeholder="年-月-日"
                                style="width: 120px"
                                @change="calculateWorkYearsAndMonths(record)"
                              />
                            </div>
                          </div>
                          <span class="date-separator">至</span>
                          <div class="ant-form-item-control-input">
                            <div class="ant-form-item-control-input-content">
                              <input
                                v-cleave="{ date: true, delimiter: '-', datePattern: ['Y', 'm', 'd'] }"
                                v-model="record.endDate"
                                class="ant-input css-dev-only-do-not-override-udyjmm"
                                placeholder="年-月-日"
                                style="width: 120px"
                                @change="calculateWorkYearsAndMonths(record)"
                              />
                            </div>
                          </div>
                        </div>
                      </template>
                      <span v-else class="field-value">
                        {{ formatDateRange(record.startDate, record.endDate) }}
                      </span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="8">
                    <a-form-item
                      label="就职单位"
                      name="company"
                      :rules="[{ required: true, message: '请输入就职单位', trigger: 'submit' }]"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                      class="form-field"
                    >
                      <AutoComplete category="occu_company" v-if="record._editMode" v-model:value="record.company" placeholder="请输入就职单位" />
                      <span v-else class="field-value">{{ record.company || '-' }}</span>
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row>
                  <a-col :span="8">
                    <a-form-item
                      label="车间"
                      name="workshop"
                      :rules="[{ required: false, message: '请输入车间' }]"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                      class="form-field"
                    >
                      <a-input v-if="record._editMode" v-model:value="record.workshop" placeholder="请输入车间" />
                      <span v-else class="field-value">{{ record.workshop || '-' }}</span>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item
                      label="工种"
                      name="workName"
                      :rules="[{ required: true, message: '请输入工种' }]"
                      :label-col="{ span: 4 }"
                      :wrapper-col="{ span: 20 }"
                      class="form-field"
                    >
                      <WorktypeAutoComplete
                        v-if="record._editMode"
                        v-model:value="record.workName"
                        placeholder="请输入工种名称或助记码"
                        :allowAutoCreate="true"
                        searchType="both"
                        style="min-width: 160px"
                        @select="handleWorktypeSelect"
                        @create="handleWorktypeCreate"
                      />
                      <span v-else class="field-value">{{ record.workName || '-' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="8">
                    <a-form-item label="接害工龄" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="form-field work-duration">
                      <template v-if="record._editMode">
                        <div class="duration-inputs">
                          <a-input-number v-model:value="record.riskYears" placeholder="年" :min="0" style="width: 60px" />
                          <span>年</span>
                          <a-input-number v-model:value="record.riskMonths" placeholder="月" :min="0" :max="11" style="width: 60px" />
                          <span>月</span>
                        </div>
                      </template>
                      <span v-else class="field-value">
                        {{ formatDuration(record.riskYears, record.riskMonths) }}
                      </span>
                    </a-form-item>
                  </a-col>
                </a-row>

                <a-row>
                  <a-col :span="8">
                    <a-form-item label="危害因素" name="riskName" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="form-field">
                      <AutoComplete
                        v-if="record._editMode"
                        category="occu_risk_factor"
                        v-model:value="record.riskName"
                        placeholder="请输入危害因素"
                        :disabled="disabled"
                        style="min-width: 180px"
                      />
                      <span v-else class="field-value">{{ record.riskName || '-' }}</span>
                    </a-form-item>
                  </a-col>
                  <a-col :span="8">
                    <a-form-item label="是否防护" name="protectFlag" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="form-field">
                      <a-switch v-if="record._editMode" v-model:checked="record.protectFlag" checked-children="是" un-checked-children="否" />
                      <span v-else class="field-value">{{ record.protectFlag ? '是' : '否' }}</span>
                    </a-form-item>
                  </a-col>

                  <a-col :span="8">
                    <a-form-item label="防护措施" name="protectMeasures" :label-col="{ span: 4 }" :wrapper-col="{ span: 20 }" class="form-field">
                      <j-dict-select-tag
                        v-if="record._editMode"
                        v-model:value="record.protectMeasures"
                        dictCode="protect_means"
                        :disabled="disabled"
                      />
                      <span v-else class="field-value">{{ record.protectMeasures || '-' }}</span>
                    </a-form-item>
                  </a-col>
                </a-row>
              </div>

              <div class="form-actions">
                <template v-if="record._editMode">
                  <a-button type="primary" size="small" @click="handleSave(record, index)" :loading="record._saving" :disabled="disabled">
                    保存
                  </a-button>
                  <a-button size="small" @click="handleCancel(record, index)" style="margin-left: 8px"> 取消 </a-button>
                </template>
                <template v-else>
                  <a-button type="link" size="small" @click="handleEdit(record, index)" :disabled="disabled"> 编辑 </a-button>
                  <a-popconfirm title="确定要删除这条记录吗？" @confirm="handleDelete(record, index)" :disabled="disabled">
                    <a-button type="link" danger size="small" :disabled="disabled"> 删除 </a-button>
                  </a-popconfirm>
                </template>
              </div>
            </a-form>
          </div>
        </div>
      </a-spin>
    </div>
  </a-card>
</template>

<script lang="ts" setup>
  import { ref, reactive, inject, watch, onMounted } from 'vue';
  import { PlusOutlined, CalculatorOutlined } from '@ant-design/icons-vue';
  import { message } from 'ant-design-vue';
  import { zyInquiryOccuHistoryList, zyInquiryOccuHistorySaveOrUpdate, zyInquiryOccuHistoryDelete } from '../ZyInquiry.api';
  import { JAsyncSearchSelect } from '@/components/Form';
  import JSelectMultiple from '../../../components/Form/src/jeecg/components/JSelectMultiple.vue';
  import JDictSelectTag from '../../../components/Form/src/jeecg/components/JDictSelectTag.vue';
  import WorktypeAutoComplete from '@/components/occu/WorktypeAutoComplete.vue';
  import { buildUUID } from '@/utils/uuid';
  import AutoComplete from '@/components/basicinfo/AutoComplete.vue';

  /**日期计算*/
  const dayjs = inject('$dayjs');

  interface OccupationalHistoryRecord {
    id?: string;
    uuid?: string; // 添加UUID字段用于卡片管理
    startDate: string;
    endDate: string;
    company: string;
    workYears?: number;
    workMonths?: number;
    riskYears?: number;
    riskMonths?: number;
    workshop: string;
    workName: string;
    riskName: string;
    protectFlag: boolean;
    protectMeasures: string;
    inquiryId: string;
    _editMode?: boolean;
    _isNew?: boolean;
    _saving?: boolean;
    _originalData?: any;
  }

  // Props
  interface Props {
    disabled?: boolean;
  }
  const props = withDefaults(defineProps<Props>(), {
    disabled: false,
  });

  // Injected values
  const inquiryMainId = inject('inquiryMainId', ref(''));
  const inquiryReady = inject('inquiryReady', ref(false));
  const customerReg = inject('customerReg', ref(null));

  // State
  const loading = ref(false);
  const dataSource = ref<OccupationalHistoryRecord[]>([]);
  const formRefs = ref<any[]>([]);

  // Set form ref for specific index
  const setFormRef = (el: any, index: number) => {
    if (el) {
      formRefs.value[index] = el;
    }
  };

  // Format date range display
  const formatDateRange = (startDate: string, endDate: string) => {
    if (!startDate && !endDate) return '-';
    const start = startDate ? (startDate.length > 10 ? startDate.substr(0, 10) : startDate) : '';
    const end = endDate ? (endDate.length > 10 ? endDate.substr(0, 10) : endDate) : '';
    return start && end ? `${start} 至 ${end}` : start || end;
  };

  // Format duration display
  const formatDuration = (years?: number, months?: number) => {
    if (!years && !months) return '-';
    let result = '';
    if (years) result += `${years}年`;
    if (months) result += `${months}月`;
    return result || '-';
  };

  /**计算工龄*/
  function calculateWorkYearsAndMonths(record: OccupationalHistoryRecord) {
    let startDateStr = record.startDate;
    let endDateStr = record.endDate;
    if (!startDateStr || !endDateStr) {
      return;
    }
    try {
      const startDate = dayjs(startDateStr);
      const endDate = dayjs(endDateStr);

      const totalMonths = endDate.diff(startDate, 'month');

      const years = Math.floor(totalMonths / 12);
      const months = totalMonths % 12;

      record.workYears = years;
      record.workMonths = months;
      record.riskYears = years;
      record.riskMonths = months;
    } catch (e) {
      console.error(e);
    }
  }

  /**根据工龄计算开始结束日期*/
  function calculateDatesFromWorkYears(record: OccupationalHistoryRecord) {
    const years = record.workYears || 0;
    const months = record.workMonths || 0;

    if (years === 0 && months === 0) {
      message.warning('请先输入工龄');
      return;
    }

    try {
      // 以当前日期为结束日期
      const endDate = dayjs();
      // 向前推算开始日期
      const startDate = endDate.subtract(years, 'year').subtract(months, 'month');

      // 更新日期字段
      record.endDate = endDate.format('YYYY-MM-DD');
      record.startDate = startDate.format('YYYY-MM-DD');

      // 同步更新接害工龄
      record.riskYears = years;
      record.riskMonths = months;

      //message.success(`已根据工龄自动计算日期：${record.startDate} 至 ${record.endDate}`);
    } catch (e) {
      console.error('计算日期失败:', e);
      message.error('计算日期失败');
    }
  }

  /**工龄年数变化处理*/
  function handleWorkYearsChange(record: OccupationalHistoryRecord) {
    // 如果接害工龄年数大于总工龄年数，自动调整接害工龄
    if (record.riskYears && record.workYears && record.riskYears > record.workYears) {
      record.riskYears = record.workYears;
      record.riskMonths = record.workMonths || 0;
      message.info('接害工龄已自动调整为不超过总工龄');
    }
  }

  /**工龄月数变化处理*/
  function handleWorkMonthsChange(record: OccupationalHistoryRecord) {
    // 如果接害工龄大于总工龄，自动调整接害工龄
    const totalWorkMonths = (record.workYears || 0) * 12 + (record.workMonths || 0);
    const totalRiskMonths = (record.riskYears || 0) * 12 + (record.riskMonths || 0);

    if (totalRiskMonths > totalWorkMonths) {
      record.riskYears = record.workYears || 0;
      record.riskMonths = record.workMonths || 0;
      message.info('接害工龄已自动调整为不超过总工龄');
    }
  }

  // Load data
  async function loadData() {
    console.log('OccupationalHistoryList: loadData triggered.');
    if (!inquiryMainId.value) {
      console.log('OccupationalHistoryList: inquiryMainId is missing, aborting load.');
      return;
    }

    try {
      loading.value = true;
      const result = await zyInquiryOccuHistoryList({ inquiryId: inquiryMainId.value });
      console.log('OccupationalHistoryList: API result', result);

      if (result && result.records) {
        dataSource.value = result.records.map((item: any) => ({
          ...item,
          uuid: item.uuid || buildUUID(), // 为回显数据生成UUID
          protectFlag: !!item.protectFlag,
          _editMode: false,
          _isNew: false,
          _saving: false,
        }));
        //console.log('OccupationalHistoryList: dataSource after fetch', JSON.stringify(dataSource.value));
        //console.log('OccupationalHistoryList: customerReg injected', JSON.stringify(customerReg.value));

        if (dataSource.value.length === 0 && customerReg.value) {
          //console.log('OccupationalHistoryList: No existing records and customerReg is present. Attempting to create a new record.');
          const { workYears, workMonths, workType, workShop: workshop, riskFactor: riskName, companyName: company } = customerReg.value;
          //console.log('OccupationalHistoryList: Destructured customerReg data', { workYears, workMonths, workshop, riskName, company });

          if (workYears > 0 || workMonths > 0) {
            //console.log('OccupationalHistoryList: workYears or workMonths is positive. Proceeding to create record.');
            const endDate = dayjs();
            let startDate = endDate.subtract(workYears, 'year');
            if (workMonths) {
              startDate = startDate.subtract(workMonths, 'month');
            }
            console.log('OccupationalHistoryList: Calculated dates', {
              startDate: startDate.format('YYYY-MM-DD'),
              endDate: endDate.format('YYYY-MM-DD'),
            });

            const newRecord: OccupationalHistoryRecord = {
              startDate: startDate.format('YYYY-MM-DD'),
              endDate: endDate.format('YYYY-MM-DD'),
              company: company || '',
              workYears: workYears || 0,
              workMonths: workMonths || 0,
              riskYears: workYears || 0,
              riskMonths: workMonths || 0,
              workshop: workshop || '',
              workName: workType || '', //工种没有现成字段，留空
              riskName: riskName || '',
              protectFlag: false,
              protectMeasures: '',
              inquiryId: inquiryMainId.value,
              _editMode: true,
              _isNew: true,
              _saving: false,
            };
            console.log('OccupationalHistoryList: Created new record object', JSON.stringify(newRecord));
            dataSource.value.push(newRecord);
            console.log('OccupationalHistoryList: dataSource after pushing new record', JSON.stringify(dataSource.value));
          } else {
            console.log('OccupationalHistoryList: No workYears or workMonths, skipping record creation.');
          }
        } else {
          console.log('OccupationalHistoryList: Conditions not met for creating a new record.', {
            hasRecords: dataSource.value.length > 0,
            hasCustomerReg: !!customerReg.value,
          });
          // 如果没有历史记录且没有customerReg数据，自动添加一个空卡片
          if (dataSource.value.length === 0) {
            addEmptyCard();
          }
        }
      } else {
        // 如果没有返回数据，也添加一个空卡片
        dataSource.value = [];
        addEmptyCard();
      }
    } catch (error) {
      console.error('加载职业史记录失败:', error);
      message.error('加载数据失败');
      dataSource.value = [];
    } finally {
      loading.value = false;
    }
  }

  // Add new record
  function handleAdd() {
    const newRecord: OccupationalHistoryRecord = {
      uuid: buildUUID(), // 为新记录生成UUID
      startDate: '',
      endDate: '',
      company: '',
      workYears: undefined,
      workMonths: undefined,
      riskYears: undefined,
      riskMonths: undefined,
      workshop: '',
      workName: '',
      riskName: '',
      protectFlag: false,
      protectMeasures: '',
      inquiryId: inquiryMainId.value,
      _editMode: true,
      _isNew: true,
      _saving: false,
    };
    dataSource.value.push(newRecord);
  }

  // 添加空卡片的方法，供父组件调用
  async function addEmptyCard() {
    // 等待数据加载完成
    if (loading.value) {
      // 如果正在加载，等待加载完成
      await new Promise((resolve) => {
        const unwatch = watch(
          () => loading.value,
          (isLoading) => {
            if (!isLoading) {
              unwatch();
              resolve(true);
            }
          }
        );
      });
    }

    // 如果已经有记录了，就不添加空卡片
    if (dataSource.value.length > 0) {
      return;
    }
    handleAdd();
  }

  // Edit record
  function handleEdit(record: OccupationalHistoryRecord, index: number) {
    // Store original data for cancel operation
    record._originalData = { ...record };
    record._editMode = true;
  }

  // Save record
  async function handleSave(record: OccupationalHistoryRecord, index: number) {
    try {
      // Validate form
      const formRef = formRefs.value[index];
      if (formRef) {
        await formRef.validate();
      }

      // ��验开始结束日期的合法性
      if (!record.startDate || !record.endDate) {
        message.error('请输入开始日期和结束日期');
        return;
      }

      if (!dayjs(record.startDate).isValid() || !dayjs(record.endDate).isValid()) {
        message.error('日期格式不正确');
        return;
      }

      const startDate = dayjs(record.startDate);
      const endDate = dayjs(record.endDate);
      if (endDate.isBefore(startDate)) {
        message.error('结束日期不能早于开始日期');
        return;
      }

      // 检查日期范围
      const currentDate = dayjs();
      const minDate = dayjs('1900-01-01');

      if (startDate.isBefore(minDate) || endDate.isBefore(minDate)) {
        message.error('日期不能早于1900年');
        return;
      }

      if (startDate.isAfter(currentDate) || endDate.isAfter(currentDate)) {
        message.error('日期不能晚于当前日期');
        return;
      }

      record._saving = true;
      const isUpdate = !!record.id;
      const saveData = {
        id: record.id,
        startDate: record.startDate,
        endDate: record.endDate,
        company: record.company,
        workYears: record.workYears,
        workMonths: record.workMonths,
        riskYears: record.riskYears,
        riskMonths: record.riskMonths,
        workshop: record.workshop,
        workName: record.workName,
        riskName: record.riskName,
        protectFlag: record.protectFlag ? 1 : 0,
        protectMeasures: record.protectMeasures,
        inquiryId: inquiryMainId.value,
      };

      const result = await zyInquiryOccuHistorySaveOrUpdate(saveData, isUpdate);
      if (result && result.success) {
        // 处理新增记录的ID更新
        if (!isUpdate && result.result) {
          // 根据后端返回的数据结构更新ID
          if (typeof result.result === 'string') {
            record.id = result.result;
          } else if (result.result.id) {
            record.id = result.result.id;
            // 如果后端返回了完整对象，更新其他字段
            Object.keys(result.result).forEach((key) => {
              if (key !== 'uuid' && record.hasOwnProperty(key)) {
                record[key] = result.result[key];
              }
            });
          }
          console.log(`✅ 新增职业史记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        } else if (isUpdate) {
          console.log(`✅ 更新职业史记录成功，ID: ${record.id}, UUID: ${record.uuid}`);
        }

        record._editMode = false;
        record._isNew = false;
        record._originalData = undefined;
        message.success(isUpdate ? '更新成功' : '保存成功');
      } else {
        throw new Error(result?.message || '保存失败');
      }
    } catch (error: any) {
      console.error('保存失败:', error);
      message.error(error.message || '保存失败');
    } finally {
      record._saving = false;
    }
  }

  // Cancel edit
  function handleCancel(record: OccupationalHistoryRecord, index: number) {
    if (record._isNew) {
      // Remove new record
      dataSource.value.splice(index, 1);
    } else {
      // Restore original data
      if (record._originalData) {
        Object.assign(record, record._originalData);
        record._originalData = undefined;
      }
      record._editMode = false;
    }
  }

  // Delete record
  async function handleDelete(record: OccupationalHistoryRecord, index: number) {
    try {
      // 如果是新记录（没有ID），直接从列表中移除
      if (record._isNew || !record.id) {
        console.log(`🗑️ 删除新建的职业史记录，UUID: ${record.uuid}`);
        dataSource.value.splice(index, 1);
        message.success('删除成功');
        return;
      }

      // 如果是已保存的记录，调用后端删除接口
      console.log(`🗑️ 删除职业史记录，ID: ${record.id}, UUID: ${record.uuid}`);
      await zyInquiryOccuHistoryDelete({ id: record.id }, () => {
        dataSource.value.splice(index, 1);
        message.success('删除成功');
      });
    } catch (error) {
      console.error('删除失败:', error);
      message.error('删除失败');
    }
  }

  // Handle worktype selection
  function handleWorktypeSelect(value: string, option: any) {
    console.log('工种选择:', value, option);
    if (option.isNew) {
      message.success(`工种"${value}"已自动创建`);
    }
  }

  // Handle worktype creation
  function handleWorktypeCreate(newWorktype: any) {
    console.log('新创建的工种:', newWorktype);
    message.success(`工种"${newWorktype.name}"创建成功`);
  }

  // Watch for inquiry ready state changes
  watch(
    () => inquiryReady.value,
    (ready) => {
      if (ready) {
        loadData();
      }
    },
    { immediate: true }
  );

  // Watch for inquiry main ID changes
  watch(
    () => inquiryMainId.value,
    (newId) => {
      if (newId && inquiryReady.value) {
        loadData();
      }
    }
  );

  onMounted(() => {
    if (inquiryMainId.value && inquiryReady.value) {
      loadData();
    }
  });

  // 暴露方法给父组件
  defineExpose({
    addEmptyCard,
  });

</script>

<style lang="less" scoped>
  .occupational-history-list {
    .empty-state {
      text-align: center;
      padding: 60px 20px;
      border-radius: 8px;
      border: 1px dashed #d9d9d9;
    }

    .inline-form-list {
      .form-item-row {
        background: #fff;
        border: 1px solid #e8e8e8;
        border-radius: 8px;
        padding: 16px;
        margin-bottom: 16px;
        box-shadow: 0 2px 4px rgba(0, 0, 0, 0.02);
        transition: all 0.3s ease;

        &:hover {
          box-shadow: 0 4px 8px rgba(0, 0, 0, 0.08);
        }

        &.editing {
          border-color: #1890ff;
          box-shadow: 0 0 0 2px rgba(24, 144, 255, 0.2);
        }

        &.new-record {
          border-color: #52c41a;
          background: #f6ffed;
        }

        .card-form {
          display: flex;
          flex-direction: column;
          gap: 12px;
        }

        .form-content {
          width: 100%;

          .form-field {
            //margin-bottom: 12px;
            .field-value {
              color: #262626;
              font-size: 14px;
              min-height: 32px;
              display: inline-flex;
              align-items: center;
              line-height: 1.5;
            }

            &.date-field {
              .date-range {
                display: flex;
                align-items: center;
                gap: 12px;

                .date-separator {
                  color: #8c8c8c;
                  font-weight: 500;
                }
              }
            }

            &.work-duration {
              .duration-inputs {
                display: flex;
                align-items: center;
                gap: 6px;

                span {
                  color: #8c8c8c;
                  font-size: 13px;
                  font-weight: 500;
                }

                .ant-btn-link {
                  padding: 2px 6px;
                  height: 28px;
                  font-size: 12px;
                  color: #1890ff;
                  border: 1px solid #d9d9d9;
                  border-radius: 4px;
                  background: #fafafa;
                  margin-left: 8px;

                  &:hover {
                    color: #40a9ff;
                    border-color: #40a9ff;
                    background: #f0f8ff;
                  }

                  &:disabled {
                    color: #d9d9d9;
                    border-color: #f0f0f0;
                    background: #f5f5f5;
                  }
                }
              }
            }
          }
        }

        .form-actions {
          display: flex;
          justify-content: flex-end;
          align-items: center;
          gap: 8px;
          padding-top: 12px;
          border-top: 1px solid #f0f0f0;
        }
      }
    }
  }

  @media (max-width: 768px) {
    .occupational-history-list {
      .inline-form-list {
        .form-item-row {
          padding: 16px;

          .form-content {
            .form-field {
              width: 100%;

              &.date-field {
                .date-range {
                  flex-wrap: wrap;
                  gap: 8px;
                }
              }

              &.work-duration {
                .duration-inputs {
                  flex-wrap: wrap;
                  gap: 4px;
                }
              }
            }
          }
        }
      }
    }
  }
</style>
