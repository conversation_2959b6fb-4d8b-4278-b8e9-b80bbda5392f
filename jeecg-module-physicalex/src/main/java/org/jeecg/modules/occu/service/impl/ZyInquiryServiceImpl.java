package org.jeecg.modules.occu.service.impl;

import cn.hutool.core.date.DateUtil;
import com.baomidou.mybatisplus.core.conditions.query.LambdaQueryWrapper;
import com.baomidou.mybatisplus.extension.service.impl.ServiceImpl;
import org.apache.commons.lang3.StringUtils;
import org.jeecg.modules.occu.entity.*;
import org.jeecg.modules.occu.mapper.*;
import org.jeecg.modules.occu.service.IZyInquiryOccuHistoryService;
import org.jeecg.modules.occu.service.IZyInquiryService;
import org.jeecg.modules.occu.service.IZyRiskFactorService;
import org.jeecg.modules.occu.utils.SmartFillAlgorithm;
import org.jeecg.modules.reg.entity.CustomerReg;
import org.jeecg.modules.reg.mapper.CustomerRegMapper;
import org.springframework.beans.factory.annotation.Autowired;
import org.springframework.jdbc.core.JdbcTemplate;
import org.springframework.stereotype.Service;
import org.springframework.transaction.annotation.Transactional;

import java.io.Serializable;
import java.util.*;
import java.util.stream.Collectors;

/**
 * @Description: 职业病问诊
 * @Author: jeecg-boot
 * @Date: 2024-05-09
 * @Version: V1.0
 */
@Service
public class ZyInquiryServiceImpl extends ServiceImpl<ZyInquiryMapper, ZyInquiry> implements IZyInquiryService {

    @Autowired
    private ZyInquiryMapper zyInquiryMapper;
    @Autowired
    private ZyInquiryOccuHistoryMapper zyInquiryOccuHistoryMapper;
    @Autowired
    private ZyInquiryRadiationHistoryMapper zyInquiryRadiationHistoryMapper;
    @Autowired
    private ZyInquiryFamilyHistoryMapper zyInquiryFamilyHistoryMapper;
    @Autowired
    private ZyInquiryMaritalStatusMapper zyInquiryMaritalStatusMapper;
    @Autowired
    private ZyInquirySymptomMapper zyInquirySymptomMapper;
    @Autowired
    private ZyInquiryDiseaseHistoryMapper zyInquiryDiseaseHistoryMapper;
    @Autowired
    private JdbcTemplate jdbcTemplate;
    @Autowired
    private IZyRiskFactorService zyRiskFactorService;
    @Autowired
    private CustomerRegMapper customerRegMapper;
    @Autowired
    private SmartFillAlgorithm smartFillAlgorithm;

    @Autowired
    private IZyInquiryOccuHistoryService zyInquiryOccuHistoryService;


    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delMain(String id) {
        zyInquiryOccuHistoryMapper.deleteByMainId(id);
        zyInquiryRadiationHistoryMapper.deleteByMainId(id);
        zyInquiryFamilyHistoryMapper.deleteByMainId(id);
        zyInquiryMaritalStatusMapper.deleteByMainId(id);
        zyInquirySymptomMapper.deleteByMainId(id);
        zyInquiryDiseaseHistoryMapper.deleteByMainId(id);
        zyInquiryMapper.deleteById(id);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public void delBatchMain(Collection<? extends Serializable> idList) {
        for (Serializable id : idList) {
            zyInquiryOccuHistoryMapper.deleteByMainId(id.toString());
            zyInquiryRadiationHistoryMapper.deleteByMainId(id.toString());
            zyInquiryFamilyHistoryMapper.deleteByMainId(id.toString());
            zyInquiryMaritalStatusMapper.deleteByMainId(id.toString());
            zyInquirySymptomMapper.deleteByMainId(id.toString());
            zyInquiryDiseaseHistoryMapper.deleteByMainId(id.toString());
            zyInquiryMapper.deleteById(id);
        }
    }

    @Override
    public ZyInquiry getByRegId(String customerRegId) {
        //根据customer_reg_id获取一个
        LambdaQueryWrapper<ZyInquiry> query = new LambdaQueryWrapper<ZyInquiry>();
        query.eq(ZyInquiry::getCustomerRegId, customerRegId);
        query.last("limit 1");

        return zyInquiryMapper.selectOne(query);
    }

    @Override
    public ZyInquiry getOrGenerateInquiry4Reg(String customerRegId) throws Exception {
        if (StringUtils.isBlank(customerRegId)) {
            throw new Exception("体检登记信息为空");
        }
        LambdaQueryWrapper<ZyInquiry> query = new LambdaQueryWrapper<ZyInquiry>();
        query.eq(ZyInquiry::getCustomerRegId, customerRegId);
        query.last("limit 1");

        ZyInquiry zyInquiry = zyInquiryMapper.selectOne(query);
        if (zyInquiry == null) {
            zyInquiry = new ZyInquiry();
            zyInquiry.setNewFlag("1");
            CustomerReg reg = customerRegMapper.getLiteById(customerRegId);
            zyInquiry.setCustomerRegId(reg.getId());
            zyInquiry.setName(reg.getName());
            zyInquiry.setGender(reg.getGender());
            zyInquiry.setAge(String.valueOf(reg.getAge()));
            zyInquiry.setCustomerRegId(reg.getId());
            zyInquiry.setIdCard(reg.getIdCard());
            zyInquiry.setExamNo(reg.getExamNo());
            zyInquiry.setCustomerId(reg.getCustomerId());
            zyInquiry.setArchiveNo(reg.getArchivesNum());

            zyInquiryMapper.insert(zyInquiry);
        }else {
            zyInquiry.setNewFlag("0");
        }

        return zyInquiry;
    }

    @Override
    public ZyInquiry getInquiryByRegId(String regId) throws Exception {
        ZyInquiry zyInquiry = zyInquiryMapper.selectOne(new LambdaQueryWrapper<ZyInquiry>().eq(ZyInquiry::getCustomerRegId, regId).last("limit 1"));
        if (Objects.nonNull(zyInquiry)) {
            ZyInquiryOccuHistory inquiryOccuHistory = zyInquiryOccuHistoryMapper.selectOne(new LambdaQueryWrapper<ZyInquiryOccuHistory>().eq(ZyInquiryOccuHistory::getInquiryId, zyInquiry.getId()).last("limit 1"));
            if (Objects.nonNull(inquiryOccuHistory)) {
                String riskName = inquiryOccuHistory.getRiskName();
                String[] split = StringUtils.split(riskName, ",");
                if (split.length > 0) {
                    String riskNameStr = zyRiskFactorService.listByIds(Arrays.asList(split)).stream().map(ZyRiskFactor::getName).collect(Collectors.joining(","));
                    inquiryOccuHistory.setRiskName(riskNameStr);
                }

            }

            zyInquiry.setZyInquiryOccuHistory(inquiryOccuHistory);
            ZyInquiryDiseaseHistory inquiryDiseaseHistory = zyInquiryDiseaseHistoryMapper.selectOne(new LambdaQueryWrapper<ZyInquiryDiseaseHistory>().eq(ZyInquiryDiseaseHistory::getInquiryId, zyInquiry.getId()).last("limit 1"));
            zyInquiry.setZyInquiryDiseaseHistory(inquiryDiseaseHistory);
            ZyInquiryMaritalStatus inquiryMaritalStatus = zyInquiryMaritalStatusMapper.selectOne(new LambdaQueryWrapper<ZyInquiryMaritalStatus>().eq(ZyInquiryMaritalStatus::getInquiryId, zyInquiry.getId()).last("limit 1"));
            zyInquiry.setZyInquiryMaritalStatus(inquiryMaritalStatus);
            ZyInquiryFamilyHistory inquiryFamilyHistory = zyInquiryFamilyHistoryMapper.selectOne(new LambdaQueryWrapper<ZyInquiryFamilyHistory>().eq(ZyInquiryFamilyHistory::getInquiryId, zyInquiry.getId()).last("limit 1"));
            zyInquiry.setZyInquiryFamilyHistory(inquiryFamilyHistory);
            List<ZyInquirySymptom> zyInquirySymptoms = zyInquirySymptomMapper.selectSymptomListByInquiryId( zyInquiry.getId());
            zyInquiry.setZyInquirySymptomList(zyInquirySymptoms);
        }
        return zyInquiry;
    }

    @Override
    public List<ZyInquiry> getHistoryByIdCard(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return new ArrayList<>();
        }

        LambdaQueryWrapper<ZyInquiry> queryWrapper = new LambdaQueryWrapper<>();
        queryWrapper.eq(ZyInquiry::getIdCard, idCard)
                   .orderByDesc(ZyInquiry::getCreateTime);

        return this.list(queryWrapper);
    }

    @Override
    public List<Map<String, Object>> getCompleteHistoryByIdCard(String idCard) {
        List<ZyInquiry> historyList = getHistoryByIdCard(idCard);
        List<Map<String, Object>> result = new ArrayList<>();

        for (ZyInquiry inquiry : historyList) {
            Map<String, Object> completeData = getCompleteDataById(inquiry.getId());
            result.add(completeData);
        }

        return result;
    }

    @Override
    public Map<String, Object> getCompleteDataById(String inquiryId) {
        Map<String, Object> result = new HashMap<>();

        // 获取主问卷数据
        ZyInquiry inquiry = this.getById(inquiryId);
        if (inquiry == null) {
            return result;
        }

        result.put("basicInfo", inquiry);

        //获取登记记录
        CustomerReg reg = customerRegMapper.getLiteById(inquiry.getCustomerRegId());
        result.put("reg", reg);

        // 获取职业史
        LambdaQueryWrapper<ZyInquiryOccuHistory> occuQuery = new LambdaQueryWrapper<>();
        occuQuery.eq(ZyInquiryOccuHistory::getInquiryId, inquiryId)
                .orderByDesc(ZyInquiryOccuHistory::getStartDate);
        List<ZyInquiryOccuHistory> occupationHistory = zyInquiryOccuHistoryMapper.selectList(occuQuery);

        // 处理职业史中的危害因素名称
        for (ZyInquiryOccuHistory occuHistory : occupationHistory) {
            zyInquiryOccuHistoryService.fillName(occuHistory);
           /* String riskName = occuHistory.getRiskName();
            if (StringUtils.isNotBlank(riskName)) {
                String[] riskIds = StringUtils.split(riskName, ",");
                if (riskIds.length > 0) {
                    String riskNameStr = zyRiskFactorService.listByIds(Arrays.asList(riskIds))
                            .stream()
                            .map(ZyRiskFactor::getName)
                            .collect(Collectors.joining(","));
                    occuHistory.setRiskName(riskNameStr);
                }
            }*/
        }
        result.put("occupationHistory", occupationHistory);

        // 获取症状
        LambdaQueryWrapper<ZyInquirySymptom> symptomQuery = new LambdaQueryWrapper<>();
        symptomQuery.eq(ZyInquirySymptom::getInquiryId, inquiryId)
                   .orderByDesc(ZyInquirySymptom::getCreateTime);
        List<ZyInquirySymptom> symptoms = zyInquirySymptomMapper.selectList(symptomQuery);
        result.put("symptoms", symptoms);

        // 获取既往病史
        LambdaQueryWrapper<ZyInquiryDiseaseHistory> diseaseQuery = new LambdaQueryWrapper<>();
        diseaseQuery.eq(ZyInquiryDiseaseHistory::getInquiryId, inquiryId)
                   .orderByDesc(ZyInquiryDiseaseHistory::getDiagnoseDate);
        List<ZyInquiryDiseaseHistory> diseaseHistory = zyInquiryDiseaseHistoryMapper.selectList(diseaseQuery);
        result.put("diseaseHistory", diseaseHistory);

        // 获取放射史
        LambdaQueryWrapper<ZyInquiryRadiationHistory> radiationQuery = new LambdaQueryWrapper<>();
        radiationQuery.eq(ZyInquiryRadiationHistory::getInquiryId, inquiryId)
                     .orderByDesc(ZyInquiryRadiationHistory::getCreateTime);
        List<ZyInquiryRadiationHistory> radiationHistory = zyInquiryRadiationHistoryMapper.selectList(radiationQuery);
        result.put("radiationHistory", radiationHistory);

        // 获取家族史
        LambdaQueryWrapper<ZyInquiryFamilyHistory> familyQuery = new LambdaQueryWrapper<>();
        familyQuery.eq(ZyInquiryFamilyHistory::getInquiryId, inquiryId)
                  .orderByDesc(ZyInquiryFamilyHistory::getCreateTime);
        List<ZyInquiryFamilyHistory> familyHistory = zyInquiryFamilyHistoryMapper.selectList(familyQuery);
        result.put("familyHistory", familyHistory);

        // 获取婚姻状况
        LambdaQueryWrapper<ZyInquiryMaritalStatus> maritalQuery = new LambdaQueryWrapper<>();
        maritalQuery.eq(ZyInquiryMaritalStatus::getInquiryId, inquiryId)
                   .orderByDesc(ZyInquiryMaritalStatus::getMarriageDate);
        List<ZyInquiryMaritalStatus> maritalStatus = zyInquiryMaritalStatusMapper.selectList(maritalQuery);
        result.put("maritalStatus", maritalStatus);

        return result;
    }

    @Override
    public List<Map<String, Object>> getSmartFillRecommendations(String idCard) {
        if (StringUtils.isBlank(idCard)) {
            return new ArrayList<>();
        }

        // 1. 获取完整的历史问卷数据
        List<Map<String, Object>> historyData = getCompleteHistoryByIdCard(idCard);

        if (historyData == null || historyData.isEmpty()) {
            return new ArrayList<>();
        }

        // 2. 使用智能算法处理数据
        return smartFillAlgorithm.processHistoryData(historyData);
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> applySmartFillData(String targetRegId, String sourceInquiryId, boolean applySubQuestionnaires) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 验证参数
            if (StringUtils.isBlank(targetRegId) || StringUtils.isBlank(sourceInquiryId)) {
                result.put("success", false);
                result.put("message", "参数不能为空");
                return result;
            }

            // 2. 获取目标客户的身份证号
            CustomerReg customerReg = customerRegMapper.selectById(targetRegId);
            if (customerReg == null || StringUtils.isBlank(customerReg.getIdCard())) {
                result.put("success", false);
                result.put("message", "无法获取客户身份证信息");
                return result;
            }

            // 3. 获取该客户的所有历史问卷数据
            List<Map<String, Object>> historyData = getCompleteHistoryByIdCard(customerReg.getIdCard());
            if (historyData == null || historyData.isEmpty()) {
                result.put("success", false);
                result.put("message", "没有找到历史问卷数据");
                return result;
            }

            // 4. 使用智能算法处理历史数据，获取推荐结果
            System.out.println("开始使用智能算法处理 " + historyData.size() + " 条历史数据");
            List<Map<String, Object>> recommendations = smartFillAlgorithm.processHistoryData(historyData);
            if (recommendations == null || recommendations.isEmpty()) {
                result.put("success", false);
                result.put("message", "智能算法未生成推荐数据");
                return result;
            }
            System.out.println("智能算法生成了 " + recommendations.size() + " 条推荐数据");

            // 5. 找到指定的源问卷推荐数据
            Map<String, Object> selectedRecommendation = null;
            for (Map<String, Object> recommendation : recommendations) {
                if (sourceInquiryId.equals(recommendation.get("id"))) {
                    selectedRecommendation = recommendation;
                    break;
                }
            }

            if (selectedRecommendation == null) {
                result.put("success", false);
                result.put("message", "未找到指定的推荐数据");
                return result;
            }

            // 6. 获取或创建目标问卷
            ZyInquiry targetInquiry = getOrGenerateInquiry4Reg(targetRegId);

            // 7. 应用智能推荐的主问卷数据
            Map<String, Object> basicInfo = (Map<String, Object>) selectedRecommendation.get("basicInfo");
            if (basicInfo != null) {
                copyBasicInfoToTarget(targetInquiry, basicInfo);
                this.updateById(targetInquiry);
            }

            // 8. 应用智能合并后的子问卷数据（如果需要）
            if (applySubQuestionnaires) {
                Map<String, Object> mergedSubQuestionnaires = (Map<String, Object>) selectedRecommendation.get("mergedSubQuestionnaires");
                if (mergedSubQuestionnaires != null) {
                    System.out.println("开始应用合并后的子问卷数据:");
                    System.out.println("- 职业史: " + ((List<?>) mergedSubQuestionnaires.get("occupationHistory")).size() + " 条");
                    System.out.println("- 症状: " + ((List<?>) mergedSubQuestionnaires.get("symptoms")).size() + " 条");
                    System.out.println("- 既往病史: " + ((List<?>) mergedSubQuestionnaires.get("diseaseHistory")).size() + " 条");
                    System.out.println("- 放射史: " + ((List<?>) mergedSubQuestionnaires.get("radiationHistory")).size() + " 条");
                    System.out.println("- 家族史: " + ((List<?>) mergedSubQuestionnaires.get("familyHistory")).size() + " 条");
                    System.out.println("- 婚姻状况: " + ((List<?>) mergedSubQuestionnaires.get("maritalStatus")).size() + " 条");

                    copySubQuestionnairesToTarget(targetInquiry.getId(), mergedSubQuestionnaires);
                    System.out.println("子问卷数据应用完成");
                }
            }

            // 9. 返回成功结果，包含统计信息
            Map<String, Object> statistics = (Map<String, Object>) selectedRecommendation.get("statistics");
            result.put("success", true);
            result.put("message", "智能填写应用成功");
            result.put("inquiryId", targetInquiry.getId());
            result.put("statistics", statistics);
            result.put("score", selectedRecommendation.get("score"));
            result.put("reasons", selectedRecommendation.get("reasons"));

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "智能填写应用失败: " + e.getMessage());
            throw new RuntimeException("智能填写应用失败", e);
        }

        return result;
    }

    @Override
    @Transactional(rollbackFor = Exception.class)
    public Map<String, Object> copyFromHistoryInquiry(String targetRegId, String sourceInquiryId) {
        Map<String, Object> result = new HashMap<>();

        try {
            // 1. 验证参数
            if (StringUtils.isBlank(targetRegId) || StringUtils.isBlank(sourceInquiryId)) {
                result.put("success", false);
                result.put("message", "参数不能为空");
                return result;
            }

            // 2. 获取源问卷数据
            Map<String, Object> sourceData = getCompleteDataById(sourceInquiryId);
            if (sourceData.isEmpty()) {
                result.put("success", false);
                result.put("message", "源问卷数据不存在");
                return result;
            }

            // 3. 获取或创建目标问卷
            ZyInquiry targetInquiry = getOrGenerateInquiry4Reg(targetRegId);

            // 4. 复制主问卷数据
            Map<String, Object> basicInfo = (Map<String, Object>) sourceData.get("basicInfo");
            if (basicInfo != null) {
                copyBasicInfoToTarget(targetInquiry, basicInfo);
                this.updateById(targetInquiry);
            }

            // 5. 复制所有子问卷数据
            copySubQuestionnairesToTarget(targetInquiry.getId(), sourceData);

            result.put("success", true);
            result.put("message", "历史数据复制成功");
            result.put("inquiryId", targetInquiry.getId());

        } catch (Exception e) {
            result.put("success", false);
            result.put("message", "历史数据复制失败: " + e.getMessage());
            throw new RuntimeException("历史数据复制失败", e);
        }

        return result;
    }

    /**
     * 复制基本信息到目标问卷
     */
    private void copyBasicInfoToTarget(ZyInquiry target, Map<String, Object> basicInfo) {
        // 复制基本信息，但保留目标问卷的关键字段
        String originalId = target.getId();
        String originalCustomerRegId = target.getCustomerRegId();
        String originalIdCard = target.getIdCard();
        String originalCustomerId = target.getCustomerId();
        Date originalCreateTime = target.getCreateTime();
        String originalCreateBy = target.getCreateBy();

        // 复制数据
        if (basicInfo.get("menarche") != null) {
            target.setMenarche((Integer) basicInfo.get("menarche"));
        }
        if (basicInfo.get("menstruation") != null) {
            target.setMenstruation((Integer) basicInfo.get("menstruation"));
        }
        if (basicInfo.get("period") != null) {
            target.setPeriod((String) basicInfo.get("period"));
        }
        if (basicInfo.get("menopauseAge") != null) {
            target.setMenopauseAge((Integer) basicInfo.get("menopauseAge"));
        }
        if (basicInfo.get("childCount") != null) {
            target.setChildCount((String) basicInfo.get("childCount"));
        }
        if (basicInfo.get("abortionCount") != null) {
            target.setAbortionCount((String) basicInfo.get("abortionCount"));
        }
        if (basicInfo.get("prematureCount") != null) {
            target.setPrematureCount((String) basicInfo.get("prematureCount"));
        }
        if (basicInfo.get("stillbirth") != null) {
            target.setStillbirth((String) basicInfo.get("stillbirth"));
        }
        if (basicInfo.get("abnormalfetal") != null) {
            target.setAbnormalfetal((String) basicInfo.get("abnormalfetal"));
        }
        if (basicInfo.get("pregnancy") != null) {
            target.setPregnancy((String) basicInfo.get("pregnancy"));
        }
        if (basicInfo.get("congenitalMalformations") != null) {
            target.setCongenitalMalformations((String) basicInfo.get("congenitalMalformations"));
        }
        if (basicInfo.get("smokStatus") != null) {
            target.setSmokStatus((String) basicInfo.get("smokStatus"));
        }
        if (basicInfo.get("smokAmount") != null) {
            target.setSmokAmount((String) basicInfo.get("smokAmount"));
        }
        if (basicInfo.get("smokYears") != null) {
            target.setSmokYears((String) basicInfo.get("smokYears"));
        }
        if (basicInfo.get("drinkStatus") != null) {
            target.setDrinkStatus((String) basicInfo.get("drinkStatus"));
        }
        if (basicInfo.get("drinkAmount") != null) {
            target.setDrinkAmount((String) basicInfo.get("drinkAmount"));
        }
        if (basicInfo.get("drinkYears") != null) {
            target.setDrinkYears((String) basicInfo.get("drinkYears"));
        }

        // 恢复关键字段
        target.setId(originalId);
        target.setCustomerRegId(originalCustomerRegId);
        target.setIdCard(originalIdCard);
        target.setCustomerId(originalCustomerId);
        target.setCreateTime(originalCreateTime);
        target.setCreateBy(originalCreateBy);
        target.setUpdateTime(new Date());
    }

    /**
     * 复制子问卷数据到目标问卷
     */
    @SuppressWarnings("unchecked")
    private void copySubQuestionnairesToTarget(String targetInquiryId, Map<String, Object> sourceData) {
        // 1. 先删除目标问卷的所有子问卷数据
        clearSubQuestionnaires(targetInquiryId);

        // 2. 复制职业史
        List<Map<String, Object>> occupationHistory = (List<Map<String, Object>>) sourceData.get("occupationHistory");
        if (occupationHistory != null && !occupationHistory.isEmpty()) {
            copyOccupationHistory(targetInquiryId, occupationHistory);
        }

        // 3. 复制症状
        List<Map<String, Object>> symptoms = (List<Map<String, Object>>) sourceData.get("symptoms");
        if (symptoms != null && !symptoms.isEmpty()) {
            copySymptoms(targetInquiryId, symptoms);
        }

        // 4. 复制既往病史
        List<Map<String, Object>> diseaseHistory = (List<Map<String, Object>>) sourceData.get("diseaseHistory");
        if (diseaseHistory != null && !diseaseHistory.isEmpty()) {
            copyDiseaseHistory(targetInquiryId, diseaseHistory);
        }

        // 5. 复制放射史
        List<Map<String, Object>> radiationHistory = (List<Map<String, Object>>) sourceData.get("radiationHistory");
        if (radiationHistory != null && !radiationHistory.isEmpty()) {
            copyRadiationHistory(targetInquiryId, radiationHistory);
        }

        // 6. 复制家族史
        List<Map<String, Object>> familyHistory = (List<Map<String, Object>>) sourceData.get("familyHistory");
        if (familyHistory != null && !familyHistory.isEmpty()) {
            copyFamilyHistory(targetInquiryId, familyHistory);
        }

        // 7. 复制婚姻状况
        List<Map<String, Object>> maritalStatus = (List<Map<String, Object>>) sourceData.get("maritalStatus");
        if (maritalStatus != null && !maritalStatus.isEmpty()) {
            copyMaritalStatus(targetInquiryId, maritalStatus);
        }
    }

    /**
     * 清空目标问卷的所有子问卷数据
     */
    private void clearSubQuestionnaires(String inquiryId) {
        // 删除职业史
        LambdaQueryWrapper<ZyInquiryOccuHistory> occuQuery = new LambdaQueryWrapper<>();
        occuQuery.eq(ZyInquiryOccuHistory::getInquiryId, inquiryId);
        zyInquiryOccuHistoryMapper.delete(occuQuery);

        // 删除症状
        LambdaQueryWrapper<ZyInquirySymptom> symptomQuery = new LambdaQueryWrapper<>();
        symptomQuery.eq(ZyInquirySymptom::getInquiryId, inquiryId);
        zyInquirySymptomMapper.delete(symptomQuery);

        // 删除既往病史
        LambdaQueryWrapper<ZyInquiryDiseaseHistory> diseaseQuery = new LambdaQueryWrapper<>();
        diseaseQuery.eq(ZyInquiryDiseaseHistory::getInquiryId, inquiryId);
        zyInquiryDiseaseHistoryMapper.delete(diseaseQuery);

        // 删除放射史
        LambdaQueryWrapper<ZyInquiryRadiationHistory> radiationQuery = new LambdaQueryWrapper<>();
        radiationQuery.eq(ZyInquiryRadiationHistory::getInquiryId, inquiryId);
        zyInquiryRadiationHistoryMapper.delete(radiationQuery);

        // 删除家族史
        LambdaQueryWrapper<ZyInquiryFamilyHistory> familyQuery = new LambdaQueryWrapper<>();
        familyQuery.eq(ZyInquiryFamilyHistory::getInquiryId, inquiryId);
        zyInquiryFamilyHistoryMapper.delete(familyQuery);

        // 删除婚姻状况
        LambdaQueryWrapper<ZyInquiryMaritalStatus> maritalQuery = new LambdaQueryWrapper<>();
        maritalQuery.eq(ZyInquiryMaritalStatus::getInquiryId, inquiryId);
        zyInquiryMaritalStatusMapper.delete(maritalQuery);
    }

    /**
     * 复制职业史数据 - 根据真实实体属性
     */
    private void copyOccupationHistory(String targetInquiryId, List<Map<String, Object>> occupationHistory) {
        for (Map<String, Object> item : occupationHistory) {
            ZyInquiryOccuHistory entity = new ZyInquiryOccuHistory();
            entity.setInquiryId(targetInquiryId);

            // 根据ZyInquiryOccuHistory实体的真实属性设置
            entity.setCompany((String) item.get("company"));
            entity.setWorkName((String) item.get("workName"));
            entity.setWorkshop((String) item.get("workshop"));
            entity.setRiskName((String) item.get("riskName"));

            // 日期字段
            String startDateStr = (String) item.get("startDate");
            if (StringUtils.isNotBlank(startDateStr)) {
                entity.setStartDate(DateUtil.parse(startDateStr));
            }
            String endDateStr = (String) item.get("endDate");
            if (StringUtils.isNotBlank(endDateStr)) {
                entity.setEndDate(DateUtil.parse(endDateStr));
            }

            // 工龄信息
            if (item.get("workYears") != null) {
                entity.setWorkYears((Integer) item.get("workYears"));
            }
            if (item.get("workMonths") != null) {
                entity.setWorkMonths((Integer) item.get("workMonths"));
            }
            if (item.get("riskYears") != null) {
                entity.setRiskYears((Integer) item.get("riskYears"));
            }
            if (item.get("riskMonths") != null) {
                entity.setRiskMonths((Integer) item.get("riskMonths"));
            }

            // 防护信息
            entity.setProtectFlag((String) item.get("protectFlag"));
            entity.setProtectMeasures((String) item.get("protectMeasures"));

            // 系统字段
            entity.setCreateTime(new Date());
            entity.setSysOrgCode((String) item.get("sysOrgCode"));

            zyInquiryOccuHistoryMapper.insert(entity);
        }
    }

    /**
     * 复制症状数据 - 根据真实实体属性
     */
    private void copySymptoms(String targetInquiryId, List<Map<String, Object>> symptoms) {
        for (Map<String, Object> item : symptoms) {
            ZyInquirySymptom entity = new ZyInquirySymptom();
            entity.setInquiryId(targetInquiryId);

            // 根据ZyInquirySymptom实体的真实属性设置
            entity.setSymptomName((String) item.get("symptomName"));
            entity.setSymptom((String) item.get("symptom"));
            entity.setSeverity((String) item.get("severity"));
            entity.setRemark((String) item.get("remark"));

            // 检查信息
            String checkDateStr = (String) item.get("checkDate");
            if (StringUtils.isNotBlank(checkDateStr)) {
                entity.setCheckDate(DateUtil.parse(checkDateStr));
            }
            entity.setCheckDoctor((String) item.get("checkDoctor"));

            // 系统字段
            entity.setCreateTime(new Date());
            entity.setSysOrgCode((String) item.get("sysOrgCode"));

            zyInquirySymptomMapper.insert(entity);
        }
    }

    /**
     * 复制既往病史数据 - 根据真实实体属性
     */
    private void copyDiseaseHistory(String targetInquiryId, List<Map<String, Object>> diseaseHistory) {
        for (Map<String, Object> item : diseaseHistory) {
            ZyInquiryDiseaseHistory entity = new ZyInquiryDiseaseHistory();
            entity.setInquiryId(targetInquiryId);

            // 根据ZyInquiryDiseaseHistory实体的真实属性设置
            entity.setDisease((String) item.get("diseaseName")); // 源数据key: diseaseName -> 实体属性: disease
            entity.setTreatmentMeans((String) item.get("treatmentMeans"));

            // 诊断信息
            String diagnoseDateStr = (String) item.get("diagnosisDate");
            if (StringUtils.isNotBlank(diagnoseDateStr)) {
                entity.setDiagnoseDate(DateUtil.parse(diagnoseDateStr));
            }
            entity.setDiagnoseCompany((String) item.get("diagnoseCompany"));

            // 检查信息
            String checkDateStr = (String) item.get("checkDate");
            if (StringUtils.isNotBlank(checkDateStr)) {
                entity.setCheckDate(DateUtil.parse(checkDateStr));
            }
            entity.setCheckDoctor((String) item.get("checkDoctor"));

            // 系统字段
            entity.setCreateTime(new Date());
            entity.setSysOrgCode((String) item.get("sysOrgCode"));

            zyInquiryDiseaseHistoryMapper.insert(entity);
        }
    }

    /**
     * 复制放射史数据 - 根据真实实体属性
     */
    private void copyRadiationHistory(String targetInquiryId, List<Map<String, Object>> radiationHistory) {
        for (Map<String, Object> item : radiationHistory) {
            ZyInquiryRadiationHistory entity = new ZyInquiryRadiationHistory();
            entity.setInquiryId(targetInquiryId);

            // 根据ZyInquiryRadiationHistory实体的真实属性设置
            entity.setCompany((String) item.get("company"));
            entity.setWorkshop((String) item.get("workshop"));
            entity.setWorkName((String) item.get("workName"));

            // 工龄信息
            if (item.get("riskYears") != null) {
                entity.setRiskYears((Integer) item.get("riskYears"));
            }
            if (item.get("riskMonths") != null) {
                entity.setRiskMonths((Integer) item.get("riskMonths"));
            }

            // 放射信息
            entity.setIrradiationType((String) item.get("irradiationType"));
            entity.setExceptional((String) item.get("exceptional"));
            entity.setRadiationType((String) item.get("radiationType"));
            entity.setWorkload((String) item.get("workload"));
            entity.setCumulative((String) item.get("cumulative"));
            entity.setOverdose((String) item.get("overdose"));
            entity.setMeter((String) item.get("meter"));
            entity.setRemark((String) item.get("remark"));

            // 系统字段
            entity.setCreateTime(new Date());
            entity.setSysOrgCode((String) item.get("sysOrgCode"));

            zyInquiryRadiationHistoryMapper.insert(entity);
        }
    }

    /**
     * 复制家族史数据 - 根据真实实体属性
     */
    private void copyFamilyHistory(String targetInquiryId, List<Map<String, Object>> familyHistory) {
        for (Map<String, Object> item : familyHistory) {
            ZyInquiryFamilyHistory entity = new ZyInquiryFamilyHistory();
            entity.setInquiryId(targetInquiryId);

            // 根据ZyInquiryFamilyHistory实体的真实属性设置
            // 实体只有: relation, disease, inquiryId, sysOrgCode, createTime等基础字段
            entity.setRelation((String) item.get("relationship")); // 源数据key: relationship -> 实体属性: relation
            entity.setDisease((String) item.get("diseaseName")); // 源数据key: diseaseName -> 实体属性: disease

            // 系统字段
            entity.setCreateTime(new Date());
            entity.setSysOrgCode((String) item.get("sysOrgCode"));

            zyInquiryFamilyHistoryMapper.insert(entity);
        }
    }

    /**
     * 复制婚姻状况数据 - 根据真实实体属性
     */
    private void copyMaritalStatus(String targetInquiryId, List<Map<String, Object>> maritalStatus) {
        for (Map<String, Object> item : maritalStatus) {
            ZyInquiryMaritalStatus entity = new ZyInquiryMaritalStatus();
            entity.setInquiryId(targetInquiryId);

            // 根据ZyInquiryMaritalStatus实体的真实属性设置
            // 日期信息
            String marriageDateStr = (String) item.get("marriageDate");
            if (StringUtils.isNotBlank(marriageDateStr)) {
                entity.setMarriageDate(DateUtil.parse(marriageDateStr));
            }

            // 配偶信息
            entity.setPartnerRadiation((String) item.get("partnerRadiation"));
            entity.setPartnerHealth((String) item.get("partnerHealth"));
            entity.setRemark((String) item.get("remark"));

            // 系统字段
            entity.setCreateTime(new Date());
            entity.setSysOrgCode((String) item.get("sysOrgCode"));

            zyInquiryMaritalStatusMapper.insert(entity);
        }
    }
}
